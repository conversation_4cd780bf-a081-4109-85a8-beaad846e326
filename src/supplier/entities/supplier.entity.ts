import { Column, Entity, Index, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { PurchaseOrder } from "../../purchase-order/entities/purchase-order.entity";
import { PaymentVoucher } from "../../payment-voucher/entities";
import { GoodsReceipt } from "../../goods-receipt/entities";

@Entity()
export class Supplier extends CustomBaseEntity {
    @Index()
    @Column({ unique: true, comment: 'รหัสผู้จำหน่าย' })
    code: string;

    @Column({ comment: 'ชื่อผู้จำหน่าย' })
    name: string;

    @Column({ nullable: true, comment: 'ชื่อผู้ติดต่อ' })
    contactPerson: string;

    @Column({ nullable: true, comment: 'หมายเลขโทรศัพท์' })
    phone: string;

    @Column({ nullable: true, comment: 'อีเมล' })
    email: string;

    @Column({ type: 'text', nullable: true, comment: 'ที่อยู่' })
    address: string;

    @Column({ nullable: true, comment: 'เลขประจำตัวผู้เสียภาษี' })
    taxId: string;

    @Column({ nullable: true, comment: 'เว็บไซต์' })
    website: string;

    @Column({ type: 'text', nullable: true, comment: 'หมายเหตุ' })
    notes: string;

    @Column({ default: true, comment: 'สถานะการใช้งาน' })
    isActive: boolean;

    @OneToMany(() => PurchaseOrder, (purchaseOrder) => purchaseOrder.supplier)
    purchaseOrders: PurchaseOrder[];

    @OneToMany(() => GoodsReceipt, (gr) => gr.supplier)
    goodsReceipts: GoodsReceipt[];

    @OneToMany(() => PaymentVoucher, (pv) => pv.supplier)
    paymentVouchers: PaymentVoucher[];

    constructor(partial?: Partial<Supplier>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
