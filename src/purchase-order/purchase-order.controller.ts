import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Put, 
  Param, 
  Delete, 
  HttpCode, 
  HttpStatus, 
  ParseIntPipe, 
  Req,
  Query,
  Patch
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Request } from 'express';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from '../auth/decorators/auth.decorator';
import { PurchaseOrderService, PURCHASE_ORDER_PAGINATION_CONFIG } from './purchase-order.service';
import { CreatePurchaseOrderDto } from './dto/create-purchase-order.dto';
import { UpdatePurchaseOrderDto } from './dto/update-purchase-order.dto';
import { PurchaseOrderStatus } from './entities/purchase-order.entity';

@Controller('purchase-order')
@ApiTags('ใบสั่งซื้อ (Purchase Order)')
@Auth()
export class PurchaseOrderController {
  constructor(private readonly purchaseOrderService: PurchaseOrderService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'ดึงข้อมูลใบสั่งซื้อแบบ pagination' })
  @ApiPaginationQuery(PURCHASE_ORDER_PAGINATION_CONFIG)
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  datatables(@Paginate() query: PaginateQuery) {
    return this.purchaseOrderService.datatables(query);
  }

  @Post()
  @ApiOperation({ summary: 'สร้างใบสั่งซื้อใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างใบสั่งซื้อสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(@Req() req: Request, @Body() createPurchaseOrderDto: CreatePurchaseOrderDto) {
    const userId = req.user['sub'];
    return this.purchaseOrderService.create(createPurchaseOrderDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลใบสั่งซื้อทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  findAll() {
    return this.purchaseOrderService.findAll();
  }

  @Get('by-po-number/:poNumber')
  @ApiOperation({ summary: 'ดึงข้อมูลใบสั่งซื้อตามเลขที่ PO' })
  @ApiParam({ name: 'poNumber', description: 'เลขที่ใบสั่งซื้อ' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสั่งซื้อ' })
  findByPoNumber(@Param('poNumber') poNumber: string) {
    return this.purchaseOrderService.findByPoNumber(poNumber);
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลใบสั่งซื้อตาม ID' })
  @ApiParam({ name: 'id', description: 'รหัสใบสั่งซื้อ' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสั่งซื้อ' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.purchaseOrderService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'แก้ไขข้อมูลใบสั่งซื้อ' })
  @ApiParam({ name: 'id', description: 'รหัสใบสั่งซื้อ' })
  @ApiResponse({ status: 200, description: 'แก้ไขข้อมูลสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้องหรือไม่สามารถแก้ไขได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสั่งซื้อ' })
  update(@Param('id', ParseIntPipe) id: number, @Body() updatePurchaseOrderDto: UpdatePurchaseOrderDto) {
    return this.purchaseOrderService.update(id, updatePurchaseOrderDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบใบสั่งซื้อ' })
  @ApiParam({ name: 'id', description: 'รหัสใบสั่งซื้อ' })
  @ApiResponse({ status: 200, description: 'ลบข้อมูลสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถลบได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสั่งซื้อ' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.purchaseOrderService.remove(id);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'อนุมัติใบสั่งซื้อ' })
  @ApiParam({ name: 'id', description: 'รหัสใบสั่งซื้อ' })
  @ApiResponse({ status: 200, description: 'อนุมัติสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถอนุมัติได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสั่งซื้อ' })
  approve(@Param('id', ParseIntPipe) id: number, @Req() req: Request) {
    const userId = req.user['sub'];
    return this.purchaseOrderService.approve(id, userId);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'เปลี่ยนสถานะใบสั่งซื้อ' })
  @ApiParam({ name: 'id', description: 'รหัสใบสั่งซื้อ' })
  @ApiQuery({ name: 'status', enum: PurchaseOrderStatus, description: 'สถานะใหม่' })
  @ApiResponse({ status: 200, description: 'เปลี่ยนสถานะสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถเปลี่ยนสถานะได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสั่งซื้อ' })
  changeStatus(
    @Param('id', ParseIntPipe) id: number, 
    @Query('status') status: PurchaseOrderStatus
  ) {
    return this.purchaseOrderService.changeStatus(id, status);
  }

  @Patch(':id/receive-items')
  @ApiOperation({ summary: 'บันทึกการรับสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสใบสั่งซื้อ' })
  @ApiResponse({ status: 200, description: 'บันทึกการรับสินค้าสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสั่งซื้อ' })
  receiveItems(
    @Param('id', ParseIntPipe) id: number,
    @Body() receivedItems: { itemId: number; receivedQuantity: number; warehouseId?: number }[]
  ) {
    return this.purchaseOrderService.receiveItems(id, receivedItems);
  }
}
