import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Lot } from "./lot.entity";
import { User } from "../../user/entities/user.entity";

export enum LotTransactionType {
    IN = "in",                    // รับเข้า
    OUT = "out",                  // จ่ายออก
    SALE = "sale",                // ขาย
    ADJUSTMENT_IN = "adjustment_in",   // ปรับปรุงเพิ่ม
    ADJUSTMENT_OUT = "adjustment_out", // ปรับปรุงลด
    TRANSFER_IN = "transfer_in",       // โอนเข้า
    TRANSFER_OUT = "transfer_out",     // โอนออก
    EXPIRED = "expired",               // หมดอายุ
    DAMAGED = "damaged",               // เสียหาย
    RETURN = "return",                 // คืนสินค้า
}

@Entity()
export class LotTransaction extends CustomBaseEntity {
    @ManyToOne(() => Lot, (lot) => lot.transactions)
    lot: Lot;

    @Column({ type: 'enum', enum: LotTransactionType })
    type: LotTransactionType;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    quantity: number; // จำนวนที่เปลี่ยนแปลง (+/-)

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    balanceAfter: number; // ยอดคงเหลือหลังทำรายการ

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    unitCost: number; // ต้นทุนต่อหน่วย (สำหรับการรับเข้า)

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    totalCost: number; // ต้นทุนรวม

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    unitPrice: number; // ราคาขายต่อหน่วย (สำหรับการขาย)

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    totalPrice: number; // ราคาขายรวม

    @Column()
    transactionDate: Date; // วันที่ทำรายการ

    @Column({ type: 'text', nullable: true })
    description: string; // คำอธิบาย

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    // Reference fields
    @Column({ nullable: true })
    referenceType: string; // ประเภทเอกสารอ้างอิง (goods_receipt, invoice, adjustment, etc.)

    @Column({ nullable: true })
    referenceId: number; // ID ของเอกสารอ้างอิง

    @Column({ nullable: true })
    referenceNumber: string; // เลขที่เอกสารอ้างอิง

    @ManyToOne(() => User, { nullable: true })
    createdBy: User; // ผู้ทำรายการ

    constructor(partial?: Partial<LotTransaction>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }

    // Helper methods
    isInbound(): boolean {
        return [
            LotTransactionType.IN,
            LotTransactionType.ADJUSTMENT_IN,
            LotTransactionType.TRANSFER_IN,
            LotTransactionType.RETURN
        ].includes(this.type);
    }

    isOutbound(): boolean {
        return [
            LotTransactionType.OUT,
            LotTransactionType.SALE,
            LotTransactionType.ADJUSTMENT_OUT,
            LotTransactionType.TRANSFER_OUT,
            LotTransactionType.EXPIRED,
            LotTransactionType.DAMAGED
        ].includes(this.type);
    }

    getSignedQuantity(): number {
        return this.isInbound() ? Math.abs(this.quantity) : -Math.abs(this.quantity);
    }
}
