import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { Warehouse } from "../../warehouse/entities/warehouse.entity";
import { Supplier } from "../../supplier/entities/supplier.entity";
import { LotTransaction } from "./lot-transaction.entity";

export enum LotStatus {
    ACTIVE = "active",       // ใช้งานได้
    EXPIRED = "expired",     // หมดอายุ
    CONSUMED = "consumed",   // ใช้หมดแล้ว
    BLOCKED = "blocked",     // ถูกบล็อก
}

@Entity()
@Index(['product', 'branch', 'warehouse', 'lotNumber'], { unique: true })
export class Lot extends CustomBaseEntity {
    @Column()
    @Index()
    lotNumber: string; // หมายเลข lot

    @ManyToOne(() => Product, (product) => product.lots)
    product: Product;

    @ManyToOne(() => Branch, (branch) => branch.lots)
    branch: Branch;

    @ManyToOne(() => Warehouse, (warehouse) => warehouse.lots)
    warehouse: Warehouse;

    @ManyToOne(() => Supplier, (supplier) => supplier.lots, { nullable: true })
    supplier: Supplier;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    initialQuantity: number; // จำนวนเริ่มต้น

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    currentQuantity: number; // จำนวนคงเหลือปัจจุบัน

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    reservedQuantity: number; // จำนวนที่จอง

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    availableQuantity: number; // จำนวนที่พร้อมใช้ (currentQuantity - reservedQuantity)

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    unitCost: number; // ต้นทุนต่อหน่วยของ lot นี้

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    totalCost: number; // ต้นทุนรวมของ lot นี้

    @Column({ nullable: true })
    manufactureDate: Date; // วันที่ผลิต

    @Column({ nullable: true })
    expiryDate: Date; // วันหมดอายุ

    @Column({ nullable: true })
    batchNumber: string; // หมายเลข batch

    @Column()
    receivedDate: Date; // วันที่รับเข้า

    @Column({ type: 'enum', enum: LotStatus, default: LotStatus.ACTIVE })
    status: LotStatus;

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @Column({ nullable: true })
    goodsReceiptId: number; // อ้างอิงใบรับสินค้า

    @Column({ nullable: true })
    goodsReceiptNumber: string; // เลขที่ใบรับสินค้า

    @OneToMany(() => LotTransaction, (transaction) => transaction.lot)
    transactions: LotTransaction[];

    constructor(partial?: Partial<Lot>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }

    // Helper methods
    updateAvailableQuantity(): void {
        this.availableQuantity = this.currentQuantity - this.reservedQuantity;
    }

    isExpired(): boolean {
        if (!this.expiryDate) return false;
        return new Date() > this.expiryDate;
    }

    isAvailable(): boolean {
        return this.status === LotStatus.ACTIVE && 
               this.availableQuantity > 0 && 
               !this.isExpired();
    }

    canReserve(quantity: number): boolean {
        return this.isAvailable() && this.availableQuantity >= quantity;
    }

    reserve(quantity: number): void {
        if (!this.canReserve(quantity)) {
            throw new Error(`Cannot reserve ${quantity} units from lot ${this.lotNumber}`);
        }
        this.reservedQuantity += quantity;
        this.updateAvailableQuantity();
    }

    unreserve(quantity: number): void {
        if (this.reservedQuantity < quantity) {
            throw new Error(`Cannot unreserve ${quantity} units from lot ${this.lotNumber}`);
        }
        this.reservedQuantity -= quantity;
        this.updateAvailableQuantity();
    }

    consume(quantity: number): void {
        if (this.currentQuantity < quantity) {
            throw new Error(`Cannot consume ${quantity} units from lot ${this.lotNumber}`);
        }
        this.currentQuantity -= quantity;
        this.updateAvailableQuantity();
        
        if (this.currentQuantity === 0) {
            this.status = LotStatus.CONSUMED;
        }
    }
}
