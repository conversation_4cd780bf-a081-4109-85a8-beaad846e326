import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, IsDateString, IsBoolean, Min } from 'class-validator';

export class CreateLotDto {
    @ApiProperty({ description: 'หมายเลข lot' })
    @IsString()
    lotNumber: string;

    @ApiProperty({ description: 'ID ของสินค้า' })
    @IsNumber()
    productId: number;

    @ApiProperty({ description: 'ID ของสาขา' })
    @IsNumber()
    branchId: number;

    @ApiProperty({ description: 'ID ของคลังสินค้า' })
    @IsNumber()
    warehouseId: number;

    @ApiProperty({ description: 'ID ของผู้จำหน่าย', required: false })
    @IsOptional()
    @IsNumber()
    supplierId?: number;

    @ApiProperty({ description: 'จำนวนเริ่มต้น' })
    @IsNumber()
    @Min(0)
    initialQuantity: number;

    @ApiProperty({ description: 'ต้นทุนต่อหน่วย' })
    @IsNumber()
    @Min(0)
    unitCost: number;

    @ApiProperty({ description: 'วันที่ผลิต', required: false })
    @IsOptional()
    @IsDateString()
    manufactureDate?: string;

    @ApiProperty({ description: 'วันหมดอายุ', required: false })
    @IsOptional()
    @IsDateString()
    expiryDate?: string;

    @ApiProperty({ description: 'หมายเลข batch', required: false })
    @IsOptional()
    @IsString()
    batchNumber?: string;

    @ApiProperty({ description: 'วันที่รับเข้า' })
    @IsDateString()
    receivedDate: string;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'ID ของใบรับสินค้า', required: false })
    @IsOptional()
    @IsNumber()
    goodsReceiptId?: number;

    @ApiProperty({ description: 'เลขที่ใบรับสินค้า', required: false })
    @IsOptional()
    @IsString()
    goodsReceiptNumber?: string;
}

export class CreateLotTransactionDto {
    @ApiProperty({ description: 'ID ของ lot' })
    @IsNumber()
    lotId: number;

    @ApiProperty({ description: 'ประเภทการทำรายการ', enum: ['in', 'out', 'sale', 'adjustment_in', 'adjustment_out', 'transfer_in', 'transfer_out', 'expired', 'damaged', 'return'] })
    @IsString()
    type: string;

    @ApiProperty({ description: 'จำนวน' })
    @IsNumber()
    quantity: number;

    @ApiProperty({ description: 'ต้นทุนต่อหน่วย', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    unitCost?: number;

    @ApiProperty({ description: 'ราคาขายต่อหน่วย', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    unitPrice?: number;

    @ApiProperty({ description: 'คำอธิบาย', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'ประเภทเอกสารอ้างอิง', required: false })
    @IsOptional()
    @IsString()
    referenceType?: string;

    @ApiProperty({ description: 'ID ของเอกสารอ้างอิง', required: false })
    @IsOptional()
    @IsNumber()
    referenceId?: number;

    @ApiProperty({ description: 'เลขที่เอกสารอ้างอิง', required: false })
    @IsOptional()
    @IsString()
    referenceNumber?: string;
}

export class LotQueryDto {
    @ApiProperty({ description: 'ID ของสินค้า', required: false })
    @IsOptional()
    @IsNumber()
    productId?: number;

    @ApiProperty({ description: 'ID ของสาขา', required: false })
    @IsOptional()
    @IsNumber()
    branchId?: number;

    @ApiProperty({ description: 'ID ของคลังสินค้า', required: false })
    @IsOptional()
    @IsNumber()
    warehouseId?: number;

    @ApiProperty({ description: 'แสดงเฉพาะ lot ที่ใช้งานได้', required: false, default: true })
    @IsOptional()
    @IsBoolean()
    onlyAvailable?: boolean = true;

    @ApiProperty({ description: 'หมายเลข lot', required: false })
    @IsOptional()
    @IsString()
    lotNumber?: string;

    @ApiProperty({ description: 'สถานะ', required: false })
    @IsOptional()
    @IsString()
    status?: string;
}
