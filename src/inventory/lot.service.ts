import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, QueryRunner } from 'typeorm';
import { Lot, LotStatus } from './entities/lot.entity';
import { LotTransaction, LotTransactionType } from './entities/lot-transaction.entity';

export interface CreateLotDto {
  lotNumber: string;
  productId: number;
  branchId: number;
  warehouseId: number;
  supplierId?: number;
  initialQuantity: number;
  unitCost: number;
  manufactureDate?: Date;
  expiryDate?: Date;
  batchNumber?: string;
  receivedDate: Date;
  notes?: string;
  goodsReceiptId?: number;
  goodsReceiptNumber?: string;
}

export interface LotTransactionDto {
  lotId: number;
  type: LotTransactionType;
  quantity: number;
  unitCost?: number;
  unitPrice?: number;
  description?: string;
  notes?: string;
  referenceType?: string;
  referenceId?: number;
  referenceNumber?: string;
}

export interface LotBalanceInfo {
  lot: Lot;
  availableQuantity: number;
  averageCost: number;
  totalValue: number;
}

@Injectable()
export class LotService {
  constructor(
    @InjectRepository(Lot)
    private lotRepository: Repository<Lot>,
    @InjectRepository(LotTransaction)
    private lotTransactionRepository: Repository<LotTransaction>,
  ) {}

  async createLot(createLotDto: CreateLotDto, userId: number): Promise<Lot> {
    // ตรวจสอบว่า lot number ซ้ำหรือไม่
    const existingLot = await this.lotRepository.findOne({
      where: {
        lotNumber: createLotDto.lotNumber,
        product: { id: createLotDto.productId },
        branch: { id: createLotDto.branchId },
        warehouse: { id: createLotDto.warehouseId }
      }
    });

    if (existingLot) {
      throw new BadRequestException(`Lot number ${createLotDto.lotNumber} already exists for this product and location`);
    }

    const totalCost = createLotDto.initialQuantity * createLotDto.unitCost;

    const lot = this.lotRepository.create({
      lotNumber: createLotDto.lotNumber,
      product: { id: createLotDto.productId } as any,
      branch: { id: createLotDto.branchId } as any,
      warehouse: { id: createLotDto.warehouseId } as any,
      supplier: createLotDto.supplierId ? { id: createLotDto.supplierId } as any : null,
      initialQuantity: createLotDto.initialQuantity,
      currentQuantity: createLotDto.initialQuantity,
      reservedQuantity: 0,
      availableQuantity: createLotDto.initialQuantity,
      unitCost: createLotDto.unitCost,
      totalCost: totalCost,
      manufactureDate: createLotDto.manufactureDate,
      expiryDate: createLotDto.expiryDate,
      batchNumber: createLotDto.batchNumber,
      receivedDate: createLotDto.receivedDate,
      status: LotStatus.ACTIVE,
      notes: createLotDto.notes,
      goodsReceiptId: createLotDto.goodsReceiptId,
      goodsReceiptNumber: createLotDto.goodsReceiptNumber
    });

    const savedLot = await this.lotRepository.save(lot);

    // สร้าง transaction เริ่มต้น
    await this.createTransaction({
      lotId: savedLot.id,
      type: LotTransactionType.IN,
      quantity: createLotDto.initialQuantity,
      unitCost: createLotDto.unitCost,
      description: `Initial lot creation: ${createLotDto.lotNumber}`,
      referenceType: 'goods_receipt',
      referenceId: createLotDto.goodsReceiptId,
      referenceNumber: createLotDto.goodsReceiptNumber
    }, userId);

    return this.findOne(savedLot.id);
  }

  async createTransaction(
    transactionDto: LotTransactionDto, 
    userId: number, 
    queryRunner?: QueryRunner
  ): Promise<LotTransaction> {
    const manager = queryRunner ? queryRunner.manager : this.lotRepository.manager;

    const lot = await manager.findOne(Lot, { 
      where: { id: transactionDto.lotId },
      lock: { mode: 'pessimistic_write' }
    });

    if (!lot) {
      throw new NotFoundException(`Lot with ID ${transactionDto.lotId} not found`);
    }

    const signedQuantity = this.getSignedQuantity(transactionDto.type, transactionDto.quantity);
    const newQuantity = lot.currentQuantity + signedQuantity;

    if (newQuantity < 0) {
      throw new BadRequestException(`Insufficient quantity in lot ${lot.lotNumber}. Available: ${lot.currentQuantity}, Requested: ${Math.abs(signedQuantity)}`);
    }

    // อัปเดต lot
    lot.currentQuantity = newQuantity;
    lot.updateAvailableQuantity();

    if (newQuantity === 0) {
      lot.status = LotStatus.CONSUMED;
    }

    await manager.save(lot);

    // สร้าง transaction
    const transaction = manager.create(LotTransaction, {
      lot: { id: transactionDto.lotId } as any,
      type: transactionDto.type,
      quantity: signedQuantity,
      balanceAfter: newQuantity,
      unitCost: transactionDto.unitCost,
      totalCost: transactionDto.unitCost ? transactionDto.unitCost * Math.abs(signedQuantity) : null,
      unitPrice: transactionDto.unitPrice,
      totalPrice: transactionDto.unitPrice ? transactionDto.unitPrice * Math.abs(signedQuantity) : null,
      transactionDate: new Date(),
      description: transactionDto.description,
      notes: transactionDto.notes,
      referenceType: transactionDto.referenceType,
      referenceId: transactionDto.referenceId,
      referenceNumber: transactionDto.referenceNumber,
      createdBy: { id: userId } as any
    });

    return await manager.save(transaction);
  }

  async findOne(id: number): Promise<Lot> {
    const lot = await this.lotRepository.findOne({
      where: { id },
      relations: {
        product: true,
        branch: true,
        warehouse: true,
        supplier: true
      }
    });

    if (!lot) {
      throw new NotFoundException(`Lot with ID ${id} not found`);
    }

    return lot;
  }

  async findByProduct(
    productId: number, 
    branchId: number, 
    warehouseId: number,
    onlyAvailable: boolean = true
  ): Promise<Lot[]> {
    const queryBuilder = this.lotRepository.createQueryBuilder('lot')
      .leftJoinAndSelect('lot.product', 'product')
      .leftJoinAndSelect('lot.branch', 'branch')
      .leftJoinAndSelect('lot.warehouse', 'warehouse')
      .leftJoinAndSelect('lot.supplier', 'supplier')
      .where('lot.product.id = :productId', { productId })
      .andWhere('lot.branch.id = :branchId', { branchId })
      .andWhere('lot.warehouse.id = :warehouseId', { warehouseId });

    if (onlyAvailable) {
      queryBuilder
        .andWhere('lot.status = :status', { status: LotStatus.ACTIVE })
        .andWhere('lot.availableQuantity > 0')
        .andWhere('(lot.expiryDate IS NULL OR lot.expiryDate > :now)', { now: new Date() });
    }

    return queryBuilder
      .orderBy('lot.receivedDate', 'ASC') // FIFO
      .getMany();
  }

  async getAverageCost(productId: number, branchId: number, warehouseId: number): Promise<number> {
    const lots = await this.findByProduct(productId, branchId, warehouseId, true);
    
    if (lots.length === 0) {
      return 0;
    }

    let totalValue = 0;
    let totalQuantity = 0;

    for (const lot of lots) {
      totalValue += lot.availableQuantity * lot.unitCost;
      totalQuantity += lot.availableQuantity;
    }

    return totalQuantity > 0 ? totalValue / totalQuantity : 0;
  }

  async getLotBalances(productId: number, branchId: number, warehouseId: number): Promise<LotBalanceInfo[]> {
    const lots = await this.findByProduct(productId, branchId, warehouseId, true);
    
    return lots.map(lot => ({
      lot,
      availableQuantity: lot.availableQuantity,
      averageCost: lot.unitCost,
      totalValue: lot.availableQuantity * lot.unitCost
    }));
  }

  private getSignedQuantity(type: LotTransactionType, quantity: number): number {
    const inboundTypes = [
      LotTransactionType.IN,
      LotTransactionType.ADJUSTMENT_IN,
      LotTransactionType.TRANSFER_IN,
      LotTransactionType.RETURN
    ];

    return inboundTypes.includes(type) ? Math.abs(quantity) : -Math.abs(quantity);
  }

  async getTransactionHistory(lotId: number): Promise<LotTransaction[]> {
    return this.lotTransactionRepository.find({
      where: { lot: { id: lotId } },
      relations: { createdBy: true },
      order: { createdAt: 'DESC' }
    });
  }
}
