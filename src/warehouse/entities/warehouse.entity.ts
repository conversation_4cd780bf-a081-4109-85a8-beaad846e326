import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Branch } from "../../branch/entities/branch.entity";
import { Inventory } from "../../inventory/entities/inventory.entity";
import { GoodsReceipt } from "../../goods-receipt/entities";

export enum WarehouseStatus {
    ACTIVE = "active",     // ใช้งาน
    INACTIVE = "inactive", // ไม่ใช้งาน
    MAINTENANCE = "maintenance" // ปรับปรุง/ซ่อมแซม
}

@Entity()
export class Warehouse extends CustomBaseEntity {
    @Index()
    @Column({ unique: true })
    code: string;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ type: 'text', nullable: true })
    location: string;

    @Column({ type: 'enum', enum: WarehouseStatus, default: WarehouseStatus.ACTIVE })
    status: WarehouseStatus;

    @Column({ type: 'numeric', nullable: true })
    capacity: number; // ความจุสูงสุด (ตัน)

    @Column({ type: 'numeric', nullable: true })
    area: number; // พื้นที่ (ตารางเมตร)

    @Column({ nullable: true })
    manager: string; // ผู้จัดการคลังสินค้า

    @Column({ nullable: true })
    contact: string; // เบอร์ติดต่อ

    @ManyToOne(() => Branch, (branch) => branch.warehouses)
    branch: Branch;

    @OneToMany(() => GoodsReceipt, (gr) => gr.warehouse)
    goodsReceipts: GoodsReceipt[];

    @OneToMany(() => Inventory, (inventory) => inventory.warehouse)
    inventories: Inventory[];

    constructor(partial?: Partial<Warehouse>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
