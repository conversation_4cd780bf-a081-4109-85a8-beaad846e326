import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Put, 
  Param, 
  Delete, 
  HttpCode, 
  HttpStatus, 
  ParseIntPipe, 
  Req,
  Query,
  Patch
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { Request } from 'express';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from '../auth/decorators/auth.decorator';
import { PaymentVoucherService, PAYMENT_VOUCHER_PAGINATION_CONFIG } from './payment-voucher.service';
import { CreatePaymentVoucherDto } from './dto/create-payment-voucher.dto';
import { UpdatePaymentVoucherDto } from './dto/update-payment-voucher.dto';
import { CreatePaymentVoucherFromGrDto } from './dto/create-payment-voucher-from-gr.dto';
import { PaymentVoucherStatus } from './entities/payment-voucher.entity';

@Controller('payment-voucher')
@ApiTags('ใบสำคัญจ่าย (Payment Voucher)')
@Auth()
export class PaymentVoucherController {
  constructor(private readonly paymentVoucherService: PaymentVoucherService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'ดึงข้อมูลใบสำคัญจ่ายแบบ pagination' })
  @ApiPaginationQuery(PAYMENT_VOUCHER_PAGINATION_CONFIG)
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  datatables(@Paginate() query: PaginateQuery) {
    return this.paymentVoucherService.datatables(query);
  }

  @Get('generate-pv-number')
  @ApiOperation({ summary: 'สร้างเลขที่ใบสำคัญจ่ายอัตโนมัติ' })
  @ApiResponse({ status: 200, description: 'สร้างเลขที่สำเร็จ' })
  generatePvNumber() {
    return this.paymentVoucherService.generatePvNumber();
  }

  @Post()
  @ApiOperation({ summary: 'สร้างใบสำคัญจ่ายใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างใบสำคัญจ่ายสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(@Req() req: Request, @Body() createPaymentVoucherDto: CreatePaymentVoucherDto) {
    const userId = req.user['sub'];
    return this.paymentVoucherService.create(createPaymentVoucherDto, userId);
  }

  @Post('from-goods-receipt')
  @ApiOperation({ summary: 'สร้างใบสำคัญจ่ายจาก Goods Receipt' })
  @ApiResponse({ status: 201, description: 'สร้างใบสำคัญจ่ายสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  @ApiResponse({ status: 404, description: 'ไม่พบ Goods Receipt' })
  createFromGr(@Req() req: Request, @Body() createDto: CreatePaymentVoucherFromGrDto) {
    const userId = req.user['sub'];
    return this.paymentVoucherService.createFromGoodsReceipt(createDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลใบสำคัญจ่ายทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  findAll() {
    return this.paymentVoucherService.findAll();
  }

  @Get('by-pv-number/:pvNumber')
  @ApiOperation({ summary: 'ดึงข้อมูลใบสำคัญจ่ายตามเลขที่ PV' })
  @ApiParam({ name: 'pvNumber', description: 'เลขที่ใบสำคัญจ่าย' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสำคัญจ่าย' })
  findByPvNumber(@Param('pvNumber') pvNumber: string) {
    return this.paymentVoucherService.findByPvNumber(pvNumber);
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลใบสำคัญจ่ายตาม ID' })
  @ApiParam({ name: 'id', description: 'รหัสใบสำคัญจ่าย' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสำคัญจ่าย' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.paymentVoucherService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'อัปเดตใบสำคัญจ่าย' })
  @ApiParam({ name: 'id', description: 'รหัสใบสำคัญจ่าย' })
  @ApiResponse({ status: 200, description: 'อัปเดตสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสำคัญจ่าย' })
  update(
    @Param('id', ParseIntPipe) id: number, 
    @Body() updatePaymentVoucherDto: UpdatePaymentVoucherDto,
    @Req() req: Request
  ) {
    const userId = req.user['sub'];
    return this.paymentVoucherService.update(id, updatePaymentVoucherDto, userId);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'อนุมัติใบสำคัญจ่าย' })
  @ApiParam({ name: 'id', description: 'รหัสใบสำคัญจ่าย' })
  @ApiResponse({ status: 200, description: 'อนุมัติสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถอนุมัติได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสำคัญจ่าย' })
  approve(@Param('id', ParseIntPipe) id: number, @Req() req: Request) {
    const userId = req.user['sub'];
    return this.paymentVoucherService.approve(id, userId);
  }

  @Patch(':id/mark-as-paid')
  @ApiOperation({ summary: 'ทำเครื่องหมายว่าจ่ายเงินแล้ว' })
  @ApiParam({ name: 'id', description: 'รหัสใบสำคัญจ่าย' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        paymentReference: { type: 'string', description: 'เลขที่อ้างอิงการจ่ายเงิน' },
        paymentDate: { type: 'string', format: 'date', description: 'วันที่จ่ายเงิน' }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'ทำเครื่องหมายสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถทำเครื่องหมายได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสำคัญจ่าย' })
  markAsPaid(
    @Param('id', ParseIntPipe) id: number, 
    @Body() body: { paymentReference?: string; paymentDate?: string },
    @Req() req: Request
  ) {
    const userId = req.user['sub'];
    return this.paymentVoucherService.markAsPaid(id, userId, body.paymentReference, body.paymentDate);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบใบสำคัญจ่าย' })
  @ApiParam({ name: 'id', description: 'รหัสใบสำคัญจ่าย' })
  @ApiResponse({ status: 200, description: 'ลบสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถลบได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบสำคัญจ่าย' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.paymentVoucherService.remove(id);
  }
}
