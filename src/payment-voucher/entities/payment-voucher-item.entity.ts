import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { PaymentVoucher } from "./payment-voucher.entity";
import { PurchaseOrderItem } from "../../purchase-order/entities/purchase-order-item.entity";
import { GoodsReceiptItem } from "../../goods-receipt/entities/goods-receipt-item.entity";

export enum PaymentVoucherItemType {
    PURCHASE_ORDER = "purchase_order",    // จ่ายตาม PO
    GOODS_RECEIPT = "goods_receipt",      // จ่ายตามใบรับสินค้า
    OTHER = "other",                      // อื่นๆ
}

@Entity()
export class PaymentVoucherItem extends CustomBaseEntity {
    @ManyToOne(() => PaymentVoucher, (pv) => pv.items, { onDelete: 'CASCADE' })
    paymentVoucher: PaymentVoucher;

    @Column()
    paymentVoucherId: number;

    @Column({ type: 'enum', enum: PaymentVoucherItemType })
    itemType: PaymentVoucherItemType;

    @ManyToOne(() => PurchaseOrderItem, (poItem) => poItem.paymentVoucherItems, { nullable: true })
    purchaseOrderItem: PurchaseOrderItem;

    @Column({ nullable: true })
    purchaseOrderItemId: number;

    // @ManyToOne(() => GoodsReceiptItem, (grItem) => grItem.paymentVoucherItems, { nullable: true })
    // goodsReceiptItem: GoodsReceiptItem;

    // @Column({ nullable: true })
    // goodsReceiptItemId: number;

    @Column()
    description: string; // รายละเอียด

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    quantity: number; // จำนวน

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    unitPrice: number; // ราคาต่อหน่วย

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    totalPrice: number; // ราคารวม

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    discountAmount: number; // ส่วนลด

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxAmount: number; // ภาษี

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    netAmount: number; // ยอดสุทธิ

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    constructor(partial?: Partial<PaymentVoucherItem>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
