import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { User } from "../../user/entities/user.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { Supplier } from "../../supplier/entities/supplier.entity";
import { PurchaseOrder } from "../../purchase-order/entities/purchase-order.entity";
import { GoodsReceipt } from "../../goods-receipt/entities/goods-receipt.entity";
import { PaymentVoucherItem } from "./payment-voucher-item.entity";

export enum PaymentVoucherStatus {
    DRAFT = "draft",           // ร่าง
    PENDING = "pending",       // รออนุมัติ
    APPROVED = "approved",     // อนุมัติแล้ว
    PAID = "paid",            // จ่ายแล้ว
    CANCELLED = "cancelled",   // ยกเลิก
}

export enum PaymentMethod {
    CASH = "cash",            // เงินสด
    TRANSFER = "transfer",    // โอนเงิน
    CHEQUE = "cheque",        // เช็ค
    CREDIT = "credit",        // เครดิต
}

@Entity()
export class PaymentVoucher extends CustomBaseEntity {
    @Index()
    @Column({ unique: true })
    pvNumber: string; // เลขที่ใบสำคัญจ่าย

    @Column()
    pvDate: Date; // วันที่ใบสำคัญจ่าย

    @Column({ type: 'enum', enum: PaymentVoucherStatus, default: PaymentVoucherStatus.DRAFT })
    status: PaymentVoucherStatus;

    @Column({ type: 'enum', enum: PaymentMethod })
    paymentMethod: PaymentMethod;

    @ManyToOne(() => Supplier, (supplier) => supplier.paymentVouchers)
    supplier: Supplier;

    @Column()
    supplierId: number;

    @ManyToOne(() => Branch, (branch) => branch.paymentVouchers)
    branch: Branch;

    @Column()
    branchId: number;

    @ManyToOne(() => PurchaseOrder, (po) => po.paymentVouchers, { nullable: true })
    purchaseOrder: PurchaseOrder;

    @Column({ nullable: true })
    purchaseOrderId: number;

    @ManyToOne(() => GoodsReceipt, (gr) => gr.paymentVouchers, { nullable: true })
    goodsReceipt: GoodsReceipt;

    @Column({ nullable: true })
    goodsReceiptId: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    subtotal: number; // ยอดรวมก่อนหักส่วนลด

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    discountAmount: number; // ส่วนลด

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxAmount: number; // ภาษี

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    totalAmount: number; // ยอดรวมสุทธิ

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @Column({ type: 'text', nullable: true })
    paymentReference: string; // เลขที่อ้างอิงการจ่ายเงิน (เช่น เลขที่เช็ค, เลขที่โอน)

    @Column({ nullable: true })
    paymentDate: Date; // วันที่จ่ายเงิน

    @ManyToOne(() => User, (user) => user.paymentVouchers)
    createdBy: User;

    @Column()
    createdById: number;

    @ManyToOne(() => User, { nullable: true })
    approvedBy: User;

    @Column({ nullable: true })
    approvedById: number;

    @Column({ nullable: true })
    approvedAt: Date;

    @ManyToOne(() => User, { nullable: true })
    paidBy: User; // ผู้จ่ายเงิน

    @Column({ nullable: true })
    paidById: number;

    @OneToMany(() => PaymentVoucherItem, (item) => item.paymentVoucher, { cascade: true })
    items: PaymentVoucherItem[];

    constructor(partial?: Partial<PaymentVoucher>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
