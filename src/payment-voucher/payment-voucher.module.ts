import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentVoucherService } from './payment-voucher.service';
import { PaymentVoucherController } from './payment-voucher.controller';
import { PaymentVoucher } from './entities/payment-voucher.entity';
import { PaymentVoucherItem } from './entities/payment-voucher-item.entity';
import { GoodsReceipt } from '../goods-receipt/entities/goods-receipt.entity';
import { GoodsReceiptModule } from '../goods-receipt/goods-receipt.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PaymentVoucher,
      PaymentVoucherItem,
      GoodsReceipt
    ]),
    forwardRef(() => GoodsReceiptModule),
  ],
  controllers: [PaymentVoucherController],
  providers: [PaymentVoucherService],
  exports: [PaymentVoucherService],
})
export class PaymentVoucherModule {}
