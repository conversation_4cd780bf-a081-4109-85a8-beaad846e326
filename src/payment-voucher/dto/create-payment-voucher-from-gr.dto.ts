import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsDateString, IsArray, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentMethod } from '../entities/payment-voucher.entity';

export class PaymentItemFromGrDto {
    @ApiProperty({ description: 'รหัส GR Item' })
    @IsNumber()
    @IsNotEmpty()
    goodsReceiptItemId: number;

    @ApiProperty({ description: 'จำนวนที่จ่าย' })
    @IsNumber()
    @IsNotEmpty()
    paymentQuantity: number;

    @ApiProperty({ description: 'ราคาต่อหน่วย' })
    @IsNumber()
    @IsNotEmpty()
    unitPrice: number;

    @ApiProperty({ description: 'ส่วนลด', default: 0 })
    @IsOptional()
    @IsNumber()
    discountAmount?: number;

    @ApiProperty({ description: 'ภาษี', default: 0 })
    @IsOptional()
    @IsNumber()
    taxAmount?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;
}

export class CreatePaymentVoucherFromGrDto {
    @ApiProperty({ description: 'รหัส Goods Receipt' })
    @IsNumber()
    @IsNotEmpty()
    goodsReceiptId: number;

    @ApiProperty({ description: 'เลขที่ใบสำคัญจ่าย' })
    @IsString()
    @IsNotEmpty()
    pvNumber: string;

    @ApiProperty({ description: 'วันที่ใบสำคัญจ่าย' })
    @IsDateString()
    pvDate: string;

    @ApiProperty({ description: 'วิธีการจ่ายเงิน', enum: PaymentMethod })
    @IsEnum(PaymentMethod)
    paymentMethod: PaymentMethod;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'เลขที่อ้างอิงการจ่ายเงิน', required: false })
    @IsOptional()
    @IsString()
    paymentReference?: string;

    @ApiProperty({ description: 'วันที่จ่ายเงิน', required: false })
    @IsOptional()
    @IsDateString()
    paymentDate?: string;

    @ApiProperty({ description: 'รหัสผู้จ่ายเงิน', required: false })
    @IsOptional()
    @IsNumber()
    paidById?: number;

    @ApiProperty({ description: 'รายการที่จ่าย', type: [PaymentItemFromGrDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => PaymentItemFromGrDto)
    items: PaymentItemFromGrDto[];
}
