import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsDateString, IsArray, ValidateNested, IsEnum, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { CreatePaymentVoucherItemDto } from './create-payment-voucher-item.dto';
import { PaymentVoucherStatus, PaymentMethod } from '../entities/payment-voucher.entity';

export class CreatePaymentVoucherDto {
    @ApiProperty({ description: 'เลขที่ใบสำคัญจ่าย' })
    @IsString()
    @IsNotEmpty()
    pvNumber: string;

    @ApiProperty({ description: 'วันที่ใบสำคัญจ่าย' })
    @IsDateString()
    pvDate: string;

    @ApiProperty({ description: 'สถานะ', enum: PaymentVoucherStatus, default: PaymentVoucherStatus.DRAFT })
    @IsOptional()
    @IsEnum(PaymentVoucherStatus)
    status?: PaymentVoucherStatus;

    @ApiProperty({ description: 'วิธีการจ่ายเงิน', enum: PaymentMethod })
    @IsEnum(PaymentMethod)
    paymentMethod: PaymentMethod;

    @ApiProperty({ description: 'รหัสผู้จำหน่าย' })
    @IsNumber()
    @IsNotEmpty()
    supplierId: number;

    @ApiProperty({ description: 'รหัสสาขา' })
    @IsNumber()
    @IsNotEmpty()
    branchId: number;

    @ApiProperty({ description: 'รหัส Purchase Order', required: false })
    @IsOptional()
    @IsNumber()
    purchaseOrderId?: number;

    @ApiProperty({ description: 'รหัส Goods Receipt', required: false })
    @IsOptional()
    @IsNumber()
    goodsReceiptId?: number;

    @ApiProperty({ description: 'ยอดรวมก่อนหักส่วนลด', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    subtotal?: number;

    @ApiProperty({ description: 'ส่วนลด', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    discountAmount?: number;

    @ApiProperty({ description: 'ภาษี', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxAmount?: number;

    @ApiProperty({ description: 'ยอดรวมสุทธิ', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    totalAmount?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'เลขที่อ้างอิงการจ่ายเงิน', required: false })
    @IsOptional()
    @IsString()
    paymentReference?: string;

    @ApiProperty({ description: 'วันที่จ่ายเงิน', required: false })
    @IsOptional()
    @IsDateString()
    paymentDate?: string;

    @ApiProperty({ description: 'รหัสผู้อนุมัติ', required: false })
    @IsOptional()
    @IsNumber()
    approvedById?: number;

    @ApiProperty({ description: 'วันที่อนุมัติ', required: false })
    @IsOptional()
    @IsDateString()
    approvedAt?: string;

    @ApiProperty({ description: 'รหัสผู้จ่ายเงิน', required: false })
    @IsOptional()
    @IsNumber()
    paidById?: number;

    @ApiProperty({ description: 'รายการสินค้า', type: [CreatePaymentVoucherItemDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreatePaymentVoucherItemDto)
    items: CreatePaymentVoucherItemDto[];
}
