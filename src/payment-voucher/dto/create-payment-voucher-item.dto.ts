import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsEnum, Min } from 'class-validator';
import { PaymentVoucherItemType } from '../entities/payment-voucher-item.entity';

export class CreatePaymentVoucherItemDto {
    @ApiProperty({ description: 'ประเภทรายการ', enum: PaymentVoucherItemType })
    @IsEnum(PaymentVoucherItemType)
    itemType: PaymentVoucherItemType;

    @ApiProperty({ description: 'รหัส PO Item', required: false })
    @IsOptional()
    @IsNumber()
    purchaseOrderItemId?: number;

    @ApiProperty({ description: 'รหัส GR Item', required: false })
    @IsOptional()
    @IsNumber()
    goodsReceiptItemId?: number;

    @ApiProperty({ description: 'รายละเอียด' })
    @IsString()
    @IsNotEmpty()
    description: string;

    @ApiProperty({ description: 'จำนวน' })
    @IsNumber()
    @Min(0)
    quantity: number;

    @ApiProperty({ description: 'ราคาต่อหน่วย' })
    @IsNumber()
    @Min(0)
    unitPrice: number;

    @ApiProperty({ description: 'ราคารวม' })
    @IsNumber()
    @Min(0)
    totalPrice: number;

    @ApiProperty({ description: 'ส่วนลด', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    discountAmount?: number;

    @ApiProperty({ description: 'ภาษี', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxAmount?: number;

    @ApiProperty({ description: 'ยอดสุทธิ' })
    @IsNumber()
    @Min(0)
    netAmount: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;
}
