import { <PERSON>umn, <PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { GoodsReceipt } from "./goods-receipt.entity";
import { PurchaseOrderItem } from "../../purchase-order/entities/purchase-order-item.entity";
import { PaymentVoucherItem } from "../../payment-voucher/entities";
import { Lot } from "../../inventory/entities/lot.entity";

@Entity()
export class GoodsReceiptItem extends CustomBaseEntity {
    @ManyToOne(() => GoodsReceipt, (gr) => gr.items, { onDelete: 'CASCADE' })
    goodsReceipt: GoodsReceipt;

    @ManyToOne(() => Product, (product) => product.goodsReceiptItems)
    product: Product;

    @Column()
    productName: string;

    @Column()
    productCode: string;

    // @ManyToOne(() => PurchaseOrderItem, (poItem) => poItem.goodsReceiptItems, { nullable: true })
    // purchaseOrderItem: PurchaseOrderItem;

    // @Column({ nullable: true })
    // purchaseOrderItemId: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    receivedQuantity: number; // จำนวนที่รับจริง

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    unitPrice: number; // ราคาต่อหน่วย

    @Column({ nullable: true })
    unitName: string;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    totalPrice: number; // ราคารวม

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    // Lot related fields
    @Column({ nullable: true })
    lotNumber: string; // หมายเลข lot

    @Column({ nullable: true })
    batchNumber: string; // หมายเลข batch

    @Column({ nullable: true })
    manufactureDate: Date; // วันที่ผลิต

    @Column({ nullable: true })
    expiryDate: Date; // วันหมดอายุ

    @Column({ type: 'boolean', default: false })
    createNewLot: boolean; // สร้าง lot ใหม่หรือไม่

    @ManyToOne(() => Lot, { nullable: true })
    lot: Lot; // lot ที่เกี่ยวข้อง (ถ้ามี)

    // @OneToMany(() => PaymentVoucherItem, (pvItem) => pvItem.goodsReceiptItem)
    // paymentVoucherItems: PaymentVoucherItem[];

    constructor(partial?: Partial<GoodsReceiptItem>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
