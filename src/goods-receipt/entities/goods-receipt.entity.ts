import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { User } from "../../user/entities/user.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { Warehouse } from "../../warehouse/entities/warehouse.entity";
import { PurchaseOrder } from "../../purchase-order/entities/purchase-order.entity";
import { Supplier } from "../../supplier/entities/supplier.entity";
import { GoodsReceiptItem } from "./goods-receipt-item.entity";
import { PaymentVoucher } from "../../payment-voucher/entities";

export enum GoodsReceiptStatus {
    DRAFT = "draft",           // ร่าง
    PENDING = "pending",       // รออนุมัติ
    APPROVED = "approved",     // อนุมัติแล้ว
    COMPLETED = "completed",   // เสร็จสิ้น
    CANCELLED = "cancelled",   // ยกเลิก
}

@Entity()
export class GoodsReceipt extends CustomBaseEntity {
    @Index()
    @Column({ unique: true })
    grNumber: string; // เลขที่ใบรับสินค้า

    @Column()
    grDate: Date; // วันที่รับสินค้า

    @Column({ type: 'enum', enum: GoodsReceiptStatus, default: GoodsReceiptStatus.DRAFT })
    status: GoodsReceiptStatus;

    @ManyToOne(() => PurchaseOrder, (po) => po.goodsReceipts, { nullable: true })
    purchaseOrder: PurchaseOrder;

    @ManyToOne(() => Supplier, (supplier) => supplier.goodsReceipts)
    supplier: Supplier;

    @ManyToOne(() => Branch, (branch) => branch.goodsReceipts)
    branch: Branch;

    @ManyToOne(() => Warehouse, (warehouse) => warehouse.goodsReceipts)
    warehouse: Warehouse;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    totalAmount: number; // ยอดรวม

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @Column({ type: 'text', nullable: true })
    deliveryNote: string; // เลขที่ใบส่งของ

    @Column({ nullable: true })
    deliveryDate: Date; // วันที่ส่งของ

    @ManyToOne(() => User, (user) => user.goodsReceipts)
    receivedBy: User; // ผู้รับสินค้า

    @ManyToOne(() => User, { nullable: true })
    approvedBy: User; // ผู้อนุมัติ

    @Column({ nullable: true })
    approvedAt: Date; // วันที่อนุมัติ

    @OneToMany(() => GoodsReceiptItem, (item) => item.goodsReceipt)
    items: GoodsReceiptItem[];

    @OneToMany(() => PaymentVoucher, (pv) => pv.goodsReceipt)
    paymentVouchers: PaymentVoucher[];

    constructor(partial?: Partial<GoodsReceipt>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
