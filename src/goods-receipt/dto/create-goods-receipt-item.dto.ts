import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, IsBoolean, IsDateString, Min } from 'class-validator';

export class CreateGoodsReceiptItemDto {
    @ApiProperty({ description: 'รหัสสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    productId: number;

    @ApiProperty({ description: 'ชื่อสินค้า' })
    @IsString()
    @IsNotEmpty()
    productName: string;

    @ApiProperty({ description: 'รหัสสินค้า' })
    @IsString()
    @IsNotEmpty()
    productCode: string;

    // @ApiProperty({ description: 'รหัส PO Item', required: false })
    // @IsOptional()
    // @IsNumber()
    // purchaseOrderItemId?: number;

    // @ApiProperty({ description: 'จำนวนที่สั่ง' })
    // @IsNumber()
    // @Min(0)
    // orderedQuantity: number;

    @ApiProperty({ description: 'จำนวนที่รับจริง' })
    @IsNumber()
    @Min(0)
    receivedQuantity: number;

    @ApiProperty({ description: 'ราคาต่อหน่วย' })
    @IsNumber()
    @Min(0)
    unitPrice: number;

    @ApiProperty({ description: 'ราคารวม' })
    @IsNumber()
    @Min(0)
    totalPrice: number;

    @ApiProperty({ description: 'ต้นทุนต่อหน่วย', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    unitCost?: number;

    // Lot related fields
    @ApiProperty({ description: 'หมายเลข lot', required: false })
    @IsOptional()
    @IsString()
    lotNumber?: string;

    @ApiProperty({ description: 'หมายเลข Batch', required: false })
    @IsOptional()
    @IsString()
    batchNumber?: string;

    @ApiProperty({ description: 'วันที่ผลิต', required: false })
    @IsOptional()
    @IsDateString()
    manufactureDate?: string;

    @ApiProperty({ description: 'วันหมดอายุ', required: false })
    @IsOptional()
    @IsDateString()
    expiryDate?: string;

    @ApiProperty({ description: 'สร้าง lot ใหม่', default: false })
    @IsOptional()
    @IsBoolean()
    createNewLot?: boolean;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    // @ApiProperty({ description: 'สินค้าเสียหาย', default: false })
    // @IsOptional()
    // @IsBoolean()
    // isDefective?: boolean;

    // @ApiProperty({ description: 'จำนวนที่เสียหาย', default: 0 })
    // @IsOptional()
    // @IsNumber()
    // @Min(0)
    // defectiveQuantity?: number;
}
