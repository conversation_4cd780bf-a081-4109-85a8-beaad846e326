import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { paginate, PaginateQuery, PaginateConfig } from 'nestjs-paginate';
import { GoodsReceipt, GoodsReceiptStatus } from './entities/goods-receipt.entity';
import { GoodsReceiptItem } from './entities/goods-receipt-item.entity';
import { CreateGoodsReceiptDto } from './dto/create-goods-receipt.dto';
import { UpdateGoodsReceiptDto } from './dto/update-goods-receipt.dto';
import { CreateGoodsReceiptFromPoDto } from './dto/create-goods-receipt-from-po.dto';
import { InventoryService } from '../inventory/inventory.service';
import { TransactionType } from '../inventory/entities/inventory-transaction.entity';
import { PurchaseOrderService } from '../purchase-order/purchase-order.service';
import { PurchaseOrder } from '../purchase-order/entities/purchase-order.entity';
import { PurchaseOrderItem } from '../purchase-order/entities/purchase-order-item.entity';
import { LotService } from '../inventory/lot.service';
import { LotTransactionType } from '../inventory/entities/lot-transaction.entity';

export const GOODS_RECEIPT_PAGINATION_CONFIG: PaginateConfig<GoodsReceipt> = {
  sortableColumns: ['id', 'grNumber', 'grDate', 'status', 'totalAmount', 'createdAt'],
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['grNumber', 'notes', 'deliveryNote'],
  relations: {
    purchaseOrder: true,
    supplier: true,
    branch: true,
    warehouse: true,
    receivedBy: true,
    approvedBy: true,
    items: {
      product: true,
    }
  },
  filterableColumns: {
    status: true,
    branchId: true,
    warehouseId: true,
    supplierId: true,
    purchaseOrderId: true
  }
};

@Injectable()
export class GoodsReceiptService {
  constructor(
    @InjectRepository(GoodsReceipt)
    private readonly goodsReceiptRepository: Repository<GoodsReceipt>,
    @InjectRepository(GoodsReceiptItem)
    private readonly goodsReceiptItemRepository: Repository<GoodsReceiptItem>,
    @InjectRepository(PurchaseOrder)
    private readonly purchaseOrderRepository: Repository<PurchaseOrder>,
    @InjectRepository(PurchaseOrderItem)
    private readonly purchaseOrderItemRepository: Repository<PurchaseOrderItem>,
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
    @Inject(forwardRef(() => PurchaseOrderService))
    private readonly purchaseOrderService: PurchaseOrderService,
    private readonly lotService: LotService,
    private readonly dataSource: DataSource,
  ) {}

  async datatables(query: PaginateQuery) {
    return paginate(query, this.goodsReceiptRepository, GOODS_RECEIPT_PAGINATION_CONFIG);
  }

  async findAll() {
    return this.goodsReceiptRepository.find({
      relations: {
        purchaseOrder: true,
        supplier: true,
        branch: true,
        warehouse: true,
        receivedBy: true,
        approvedBy: true,
        items: {
          product: {
            category: true,
            unit: true
          },
        }
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number) {
    const goodsReceipt = await this.goodsReceiptRepository.findOne({
      where: { id },
      relations: {
        purchaseOrder: {
          items: {
            product: true
          }
        },
        supplier: true,
        branch: true,
        warehouse: true,
        receivedBy: true,
        approvedBy: true,
        items: {
          product: {
            category: true,
            unit: true
          },
        }
      }
    });

    if (!goodsReceipt) {
      throw new NotFoundException(`Goods Receipt with ID ${id} not found`);
    }

    return goodsReceipt;
  }

  async findByGrNumber(grNumber: string) {
    const goodsReceipt = await this.goodsReceiptRepository.findOne({
      where: { grNumber },
      relations: {
        purchaseOrder: true,
        supplier: true,
        branch: true,
        warehouse: true,
        receivedBy: true,
        approvedBy: true,
        items: {
          product: true,
        }
      }
    });

    if (!goodsReceipt) {
      throw new NotFoundException(`Goods Receipt with GR Number ${grNumber} not found`);
    }

    return goodsReceipt;
  }

  async create(createGoodsReceiptDto: CreateGoodsReceiptDto, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // ตรวจสอบว่าเลขที่ GR ซ้ำหรือไม่
      const existingGr = await queryRunner.manager.findOne(GoodsReceipt, {
        where: { grNumber: createGoodsReceiptDto.grNumber }
      });

      if (existingGr) {
        throw new BadRequestException(`Goods Receipt number ${createGoodsReceiptDto.grNumber} already exists`);
      }

      // สร้าง GoodsReceipt
      const goodsReceipt = queryRunner.manager.create(GoodsReceipt, {
        ...createGoodsReceiptDto,
        grDate: new Date(createGoodsReceiptDto.grDate),
        deliveryDate: createGoodsReceiptDto.deliveryDate ? new Date(createGoodsReceiptDto.deliveryDate) : null,
        approvedAt: createGoodsReceiptDto.approvedAt ? new Date(createGoodsReceiptDto.approvedAt) : null,
        createdBy: { id: userId } as any,
        supplier: { id: createGoodsReceiptDto.supplierId } as any,
        branch: { id: createGoodsReceiptDto.branchId } as any,
        warehouse: { id: createGoodsReceiptDto.warehouseId } as any,
        receivedBy: { id: createGoodsReceiptDto.receivedById } as any,
        approvedBy: createGoodsReceiptDto.approvedById ? { id: createGoodsReceiptDto.approvedById } as any : null,
        // purchaseOrder: createGoodsReceiptDto.purchaseOrderId ? { id: createGoodsReceiptDto.purchaseOrderId } as any : null
      });

      const savedGoodsReceipt = await queryRunner.manager.save(goodsReceipt);

      // สร้าง GoodsReceiptItems
      for (const itemDto of createGoodsReceiptDto.items) {
        const item = queryRunner.manager.create(GoodsReceiptItem, {
          ...itemDto,
          // expiryDate: itemDto.expiryDate ? new Date(itemDto.expiryDate) : null,
          goodsReceipt: savedGoodsReceipt,
          product: { id: itemDto.productId } as any,
          // purchaseOrderItem: itemDto.purchaseOrderItemId ? { id: itemDto.purchaseOrderItemId } as any : null
        });

        await queryRunner.manager.save(item);
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedGoodsReceipt.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async createFromPurchaseOrder(createDto: CreateGoodsReceiptFromPoDto, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // ตรวจสอบ PO
      const purchaseOrder = await queryRunner.manager.findOne(PurchaseOrder, {
        where: { id: createDto.purchaseOrderId },
        relations: {
          items: {
            product: true
          },
          supplier: true,
          branch: true
        }
      });

      if (!purchaseOrder) {
        throw new NotFoundException(`Purchase Order with ID ${createDto.purchaseOrderId} not found`);
      }

      // ตรวจสอบว่าเลขที่ GR ซ้ำหรือไม่
      const existingGr = await queryRunner.manager.findOne(GoodsReceipt, {
        where: { grNumber: createDto.grNumber }
      });

      if (existingGr) {
        throw new BadRequestException(`Goods Receipt number ${createDto.grNumber} already exists`);
      }

      // สร้าง GoodsReceipt
      const goodsReceipt = queryRunner.manager.create(GoodsReceipt, {
        grNumber: createDto.grNumber,
        grDate: new Date(createDto.grDate),
        deliveryDate: createDto.deliveryDate ? new Date(createDto.deliveryDate) : null,
        notes: createDto.notes,
        deliveryNote: createDto.deliveryNote,
        status: GoodsReceiptStatus.DRAFT,
        purchaseOrder: purchaseOrder,
        supplier: purchaseOrder.supplier,
        branch: purchaseOrder.branch,
        warehouse: { id: createDto.warehouseId } as any,
        receivedBy: { id: createDto.receivedById } as any,
        createdBy: { id: userId } as any,
        totalAmount: 0
      });

      const savedGoodsReceipt = await queryRunner.manager.save(goodsReceipt);

      let totalAmount = 0;

      // สร้าง GoodsReceiptItems จาก PO Items
      for (const receiveItem of createDto.items) {
        const poItem = purchaseOrder.items.find(item => item.id === receiveItem.purchaseOrderItemId);

        if (!poItem) {
          throw new NotFoundException(`Purchase Order Item with ID ${receiveItem.purchaseOrderItemId} not found`);
        }

        // ตรวจสอบจำนวนที่รับไม่เกินจำนวนที่เหลือ
        const remainingQuantity = poItem.quantity - poItem.receivedQuantity;
        if (receiveItem.receivedQuantity > remainingQuantity) {
          throw new BadRequestException(`Received quantity (${receiveItem.receivedQuantity}) exceeds remaining quantity (${remainingQuantity}) for product ${poItem.productName}`);
        }

        const totalPrice = receiveItem.receivedQuantity * poItem.unitPrice;
        totalAmount += totalPrice;

        const grItem = queryRunner.manager.create(GoodsReceiptItem, {
          goodsReceipt: savedGoodsReceipt,
          product: poItem.product,
          productName: poItem.productName,
          productCode: poItem.productCode,
          purchaseOrderItem: poItem,
          orderedQuantity: poItem.quantity,
          receivedQuantity: receiveItem.receivedQuantity,
          unitPrice: poItem.unitPrice,
          totalPrice: totalPrice,
          unitCost: receiveItem.unitCost || poItem.unitPrice,
          batchNumber: receiveItem.batchNumber,
          expiryDate: receiveItem.expiryDate ? new Date(receiveItem.expiryDate) : null,
          notes: receiveItem.notes,
          isDefective: (receiveItem.defectiveQuantity || 0) > 0,
          defectiveQuantity: receiveItem.defectiveQuantity || 0
        });

        await queryRunner.manager.save(grItem);

        // อัปเดต PO Item
        await queryRunner.manager.update(PurchaseOrderItem, poItem.id, {
          receivedQuantity: poItem.receivedQuantity + receiveItem.receivedQuantity,
          remainingQuantity: poItem.quantity - (poItem.receivedQuantity + receiveItem.receivedQuantity)
        });
      }

      // อัปเดต total amount
      await queryRunner.manager.update(GoodsReceipt, savedGoodsReceipt.id, {
        totalAmount: totalAmount
      });

      await queryRunner.commitTransaction();
      return this.findOne(savedGoodsReceipt.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async update(id: number, updateGoodsReceiptDto: UpdateGoodsReceiptDto, userId: number) {
    const goodsReceipt = await this.findOne(id);

    if (goodsReceipt.status === GoodsReceiptStatus.COMPLETED) {
      throw new BadRequestException('Cannot update completed goods receipt');
    }

    const updateData: any = { ...updateGoodsReceiptDto };

    // แปลงวันที่
    if (updateData.grDate) {
      updateData.grDate = new Date(updateData.grDate);
    }
    if (updateData.deliveryDate) {
      updateData.deliveryDate = new Date(updateData.deliveryDate);
    }
    if (updateData.approvedAt) {
      updateData.approvedAt = new Date(updateData.approvedAt);
    }

    // ลบ foreign key fields และ items ออกจาก updateData
    delete updateData.supplierId;
    delete updateData.branchId;
    delete updateData.warehouseId;
    delete updateData.receivedById;
    delete updateData.approvedById;
    delete updateData.purchaseOrderId;
    delete updateData.items;

    await this.goodsReceiptRepository.update(id, {
      ...updateData,
      lastUpdatedBy: userId
    });

    return this.findOne(id);
  }

  async approve(id: number, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const goodsReceipt = await queryRunner.manager.findOne(GoodsReceipt, {
        where: { id },
        relations: {
          items: {
            product: true
          },
          warehouse: true,
          supplier: true
        }
      });

      if (!goodsReceipt) {
        throw new NotFoundException(`Goods Receipt with ID ${id} not found`);
      }

      if (goodsReceipt.status === GoodsReceiptStatus.COMPLETED) {
        throw new BadRequestException('Goods receipt is already completed');
      }

      // อัปเดตสถานะเป็น COMPLETED และปรับปรุงสินค้าคงคลัง
      await queryRunner.manager.update(GoodsReceipt, id, {
        status: GoodsReceiptStatus.COMPLETED,
        approvedBy: { id: userId } as any,
        approvedAt: new Date(),
        // updatedBy: userId
      });

      // ปรับปรุงสินค้าคงคลังและสร้าง lot สำหรับแต่ละรายการ
      for (const item of goodsReceipt.items) {
        if (item.receivedQuantity > 0) {
          try {
            // หา inventory หรือสร้างใหม่
            let inventory;
            try {
              inventory = await this.inventoryService.findByProductAndLocation(
                item.product.id,
                goodsReceipt.branch.id,
                goodsReceipt.warehouse.id
              );
            } catch (error) {
              inventory = null;
            }

            if (!inventory) {
              // สร้าง inventory ใหม่
              inventory = await this.inventoryService.create({
                productId: item.product.id,
                branchId: goodsReceipt.branch.id,
                warehouseId: goodsReceipt.warehouse.id,
                quantity: 0,
                minStock: 0,
                maxStock: 0,
                reorderPoint: 0,
                averageCost: item.unitPrice
              });
            }

            // สร้าง lot ถ้ามีการระบุ lot number หรือต้องการสร้าง lot ใหม่
            let lot = null;
            if (item.createNewLot || item.lotNumber) {
              const lotNumber = item.lotNumber || `${goodsReceipt.grNumber}-${item.product.code}-${Date.now()}`;

              lot = await this.lotService.createLot({
                lotNumber: lotNumber,
                productId: item.product.id,
                branchId: goodsReceipt.branch.id,
                warehouseId: goodsReceipt.warehouse.id,
                supplierId: goodsReceipt.supplier.id,
                initialQuantity: item.receivedQuantity,
                unitCost: item.unitPrice,
                manufactureDate: item.manufactureDate,
                expiryDate: item.expiryDate,
                batchNumber: item.batchNumber,
                receivedDate: goodsReceipt.grDate,
                notes: item.notes,
                goodsReceiptId: goodsReceipt.id,
                goodsReceiptNumber: goodsReceipt.grNumber
              }, userId);

              // อัปเดต goods receipt item ให้อ้างอิง lot
              await queryRunner.manager.update(GoodsReceiptItem, item.id, {
                lot: { id: lot.id } as any
              });
            }

            // เพิ่มสินค้าเข้า inventory
            await this.inventoryService.createTransaction({
              inventoryId: inventory.id,
              type: TransactionType.PURCHASE,
              quantity: item.receivedQuantity,
              unitCost: item.unitPrice,
              description: `Goods Receipt: ${goodsReceipt.grNumber} - ${item.productName}${lot ? ` (Lot: ${lot.lotNumber})` : ''}`,
              referenceType: 'goods_receipt',
              referenceId: goodsReceipt.id,
              referenceNumber: goodsReceipt.grNumber,
              supplierId: goodsReceipt.supplier.id,
            }, userId);

            // อัปเดต average cost จาก lots
            await this.inventoryService.updateAverageCostFromLots(inventory.id);

          } catch (error) {
            console.error(`Error updating inventory for item ${item.productName}:`, error);
            // ไม่ throw error เพื่อไม่ให้กระทบกับการอนุมัติ
          }
        }
      }

      await queryRunner.commitTransaction();
      return this.findOne(id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const goodsReceipt = await this.findOne(id);

    if (goodsReceipt.status === GoodsReceiptStatus.COMPLETED) {
      throw new BadRequestException('Cannot delete completed goods receipt');
    }

    await this.goodsReceiptRepository.softDelete(id);
  }

  async generateGrNumber(): Promise<string> {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');

    const prefix = `GR${year}${month}`;

    // หาเลขที่ล่าสุดในเดือนนี้
    const lastGr = await this.goodsReceiptRepository
      .createQueryBuilder('gr')
      .where('gr.grNumber LIKE :prefix', { prefix: `${prefix}%` })
      .orderBy('gr.grNumber', 'DESC')
      .getOne();

    let nextNumber = 1;
    if (lastGr) {
      const lastNumber = parseInt(lastGr.grNumber.substring(prefix.length));
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${String(nextNumber).padStart(4, '0')}`;
  }
}
