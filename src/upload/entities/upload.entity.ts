import { <PERSON><PERSON><PERSON><PERSON>, AfterLoad, Colum<PERSON>, <PERSON><PERSON><PERSON>, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Customer } from "../../customer/entities/customer.entity";
import { QueueItemPhoto } from "../../queue/entities/queue-item-photo.entity";
import { Queue } from "../../queue/entities/queue.entity";
import { QueuePhoto } from "../../queue/entities/queue-photo.entity";

@Entity()
export class Upload extends CustomBaseEntity {

    @Column()
    fieldname: string;

    @Column()
    originalname: string;

    @Column()
    encoding: string;

    @Column()
    mimetype: string;

    @Column()
    destination: string;

    @Column()
    filename: string;

    @Column()
    path: string;

    @Column()
    size: number

    @Column({ default: 'local' })
    provider: string;

    @Expose()
    get pathUrl(): string {
        if (!this.filename) return null;

        // ถ้า path ขึ้นต้นด้วย http:// หรือ https:// ให้ return path เลย
        // if (this.path.startsWith('http://') || this.path.startsWith('https://')) {
        //     return this.path;
        // }

        // ถ้าไม่ใช่ URL สมบูรณ์ ให้ต่อ base URL เข้าไป
        return `${process.env.APP_URL}/${this.filename}`;
    }

    @OneToMany(() => Customer, (_) => _.identityCard, { cascade: true })
    customers: Array<Customer>;

    @OneToMany(() => QueueItemPhoto, (_) => _.upload, { cascade: true })
    queueItemPhotos: QueueItemPhoto[];

    @OneToMany(() => Queue, (_) => _.coverPhoto, { cascade: true })
    coverPhotos: Queue[];

    @OneToMany(() => QueuePhoto, (_) => _.upload, { cascade: true })
    queuePhotos: QueuePhoto[];

    constructor(partial?: Partial<Upload>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
